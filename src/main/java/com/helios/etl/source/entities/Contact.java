package com.helios.etl.source.entities;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
public class Contact {
    private int id;
    private String nom = "";
    private String prenom = "";
    private String civilite = "";
    private String email = "";
    private String telephone = "";
    private String telephone2 = "";
    private String telephone3 = "";
    private String mobile = "";
    private String fonction = "";
    private String ctNum = "";
    private String ctNo = "";
    private LocalDateTime dateCreation = LocalDateTime.MIN;
    private LocalDateTime dateModification = LocalDateTime.MIN;
    private String note = "";
    private boolean systematique = false;
    private boolean facturation = false;
    private boolean relance = false;

    /**
     * Converts this Contact entity to a Map<String, String>
     * @return Map containing all contact fields as string key-value pairs
     */
    public Map<String, String> toMap() {
        Map<String, String> contactMap = new HashMap<>();

        contactMap.put("id", String.valueOf(id));
        contactMap.put("nom", nom != null ? nom : "");
        contactMap.put("prenom", prenom != null ? prenom : "");
        contactMap.put("civilite", civilite != null ? civilite : "");
        contactMap.put("email", email != null ? email : "");
        contactMap.put("telephone", telephone != null ? telephone : "");
        contactMap.put("telephone2", telephone2 != null ? telephone2 : "");
        contactMap.put("telephone3", telephone3 != null ? telephone3 : "");
        contactMap.put("mobile", mobile != null ? mobile : "");
        contactMap.put("fonction", fonction != null ? fonction : "");
        contactMap.put("ctNum", ctNum != null ? ctNum : "");
        contactMap.put("ctNo", ctNo != null ? ctNo : "");
        contactMap.put("dateCreation", dateCreation != null ? dateCreation.toString() : "");
        contactMap.put("dateModification", dateModification != null ? dateModification.toString() : "");
        contactMap.put("note", note != null ? note : "");
        contactMap.put("systematique", String.valueOf(systematique));
        contactMap.put("facturation", String.valueOf(facturation));
        contactMap.put("relance", String.valueOf(relance));

        return contactMap;
    }
}