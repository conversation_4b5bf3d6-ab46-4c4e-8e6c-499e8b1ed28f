package com.helios.etl.services.transformers;

import com.helios.etl.model.AbstractIssue;
import com.helios.etl.model.IssuePieceJointe;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.HeliosPJ;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;

public class PjHeliosToIssuePieceJointeTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public PjHeliosToIssuePieceJointeTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public IssuePieceJointe transform(HeliosPJ pj, AbstractIssue issue) {

        if(pj == null || pj.getNom().trim().isEmpty() || pj.getPath().trim().isEmpty())
        {
            return null;
        }
        if(issue == null)
        {
            return null;
        }

        IssuePieceJointe issuePieceJointe = tryGetIssuePieceJointeFromCache(pj.getPath(), issue);
        if(issuePieceJointe != null)
        {
            return issuePieceJointe;
        }

        String libelle = pj.getNom();
        if(pj.getCommandes() != null && pj.getCommandes().getCommande() != null)
        {
            libelle = "Commande " + pj.getCommandes().getCommande() + " - " + libelle;
        }
        if(pj.getTickets() != null && pj.getTickets().getIdTickets() > 0)
        {
            libelle = "Ticket: " + pj.getTickets().getIdTickets() + " - " + libelle;
        }

        issuePieceJointe = new IssuePieceJointe();
        issuePieceJointe.setLibelle(libelle);
        issuePieceJointe.setDescription(pj.toString());
        issuePieceJointe.setFichier(pj.getPath());
        issuePieceJointe.setIssue(issue);
        return issuePieceJointe;
    }

    private IssuePieceJointe tryGetIssuePieceJointeFromCache(String pathPj, AbstractIssue issue) {
        if (_cm == null || _cm.getIssuePieceJointeRepository() == null) {
            return null;
        }

        IssuePieceJointe issuePieceJointe = lookInCollection(_cm.getIssuePieceJointes(), pathPj, issue);
        if(issuePieceJointe != null)
        {
            return issuePieceJointe;
        }

        HashSet<IssuePieceJointe> collection = new HashSet<>(_cm.getIssuePieceJointeRepository().findAll());
        issuePieceJointe = lookInCollection(collection, pathPj, issue);
        return issuePieceJointe;
    }

    private IssuePieceJointe lookInCollection(HashSet<IssuePieceJointe> collection, String pathPj, AbstractIssue issue) {
        if (collection == null || collection.isEmpty()) {
            return null;
        }

        for (IssuePieceJointe issuePieceJointe : collection) {
            if (issuePieceJointe.getFichier() != null && pathPj != null
                && issuePieceJointe.getFichier().equalsIgnoreCase(pathPj)
                && issuePieceJointe.getIssue() != null
                && issuePieceJointe.getIssue().getOid() == issue.getOid()) {
                return issuePieceJointe;
            }
        }

        return null;
    }
}
